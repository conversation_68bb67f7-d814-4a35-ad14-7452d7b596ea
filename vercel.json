{"buildCommand": "next build", "outputDirectory": ".next", "devCommand": "next dev", "installCommand": "npm install", "framework": "nextjs", "regions": ["iad1"], "github": {"silent": true}, "env": {"NEXT_PUBLIC_SUPABASE_URL": "https://wtwetyalktzkimwtiwun.supabase.co", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.44EU7VKJPO7W7Xpvf-X6zp58O0KuBYZ0seRTfLextR0", "SUPABASE_SERVICE_ROLE_KEY": "@supabase_service_role_key", "NEXT_PUBLIC_CLOUDFLARE_ACCOUNT_ID": "4ab691f283d3b63c6ce3a49e4f33f298", "CLOUDFLARE_R2_ACCESS_KEY_ID": "95cd03d4415e84138943935d120f8d57", "CLOUDFLARE_R2_SECRET_ACCESS_KEY": "aab6a0d84c0a598373642eb0f6f175aca09d47317b11e62a582c9a78f2636c43", "CLOUDFLARE_R2_BUCKET_NAME": "v<PERSON><PERSON><PERSON>", "NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL": "https://pub-8f37c342d7194c4199e9b0e6c186f62d.r2.dev"}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-store, max-age=0"}]}]}