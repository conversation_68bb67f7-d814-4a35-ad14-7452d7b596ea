<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Categories API</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1 {
      color: #0055b3;
    }
    .card {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      background-color: #f9f9f9;
    }
    .success {
      color: #22c55e;
      font-weight: bold;
    }
    .error {
      color: #ef4444;
      font-weight: bold;
    }
    button {
      background-color: #0055b3;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-right: 10px;
    }
    button:hover {
      background-color: #003b7a;
    }
    input, select {
      padding: 8px;
      margin-bottom: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      width: 100%;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    .form-group {
      margin-bottom: 15px;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    th {
      background-color: #f2f2f2;
    }
    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    .actions {
      display: flex;
      gap: 5px;
    }
    .actions button {
      padding: 5px 10px;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <h1>Test Categories API</h1>
  
  <div class="card">
    <h2>Add New Category</h2>
    <div class="form-group">
      <label for="category-name">Category Name:</label>
      <input type="text" id="category-name" placeholder="Enter category name">
    </div>
    <div class="form-group">
      <label for="category-slug">Category Slug:</label>
      <input type="text" id="category-slug" placeholder="Enter category slug">
    </div>
    <button id="add-category-btn">Add Category</button>
    <div id="add-result"></div>
  </div>
  
  <div class="card">
    <h2>All Categories</h2>
    <button id="fetch-categories-btn">Fetch Categories</button>
    <div id="categories-list"></div>
  </div>
  
  <div class="card">
    <h2>Edit Category</h2>
    <div class="form-group">
      <label for="edit-category-id">Category ID:</label>
      <select id="edit-category-id">
        <option value="">Select a category</option>
      </select>
    </div>
    <div class="form-group">
      <label for="edit-category-name">Category Name:</label>
      <input type="text" id="edit-category-name" placeholder="Enter category name">
    </div>
    <div class="form-group">
      <label for="edit-category-slug">Category Slug:</label>
      <input type="text" id="edit-category-slug" placeholder="Enter category slug">
    </div>
    <button id="update-category-btn">Update Category</button>
    <button id="delete-category-btn">Delete Category</button>
    <div id="edit-result"></div>
  </div>
  
  <script>
    // Fetch all categories
    async function fetchCategories() {
      const categoriesList = document.getElementById('categories-list');
      const editCategoryId = document.getElementById('edit-category-id');
      
      categoriesList.innerHTML = 'Loading categories...';
      
      try {
        const response = await fetch('/api/admin/categories');
        const data = await response.json();
        
        if (data.success) {
          if (data.data.length === 0) {
            categoriesList.innerHTML = 'No categories found.';
            return;
          }
          
          // Clear the select options
          editCategoryId.innerHTML = '<option value="">Select a category</option>';
          
          // Create a table to display categories
          let tableHtml = `
            <table>
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Name</th>
                  <th>Slug</th>
                  <th>Created At</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
          `;
          
          data.data.forEach(category => {
            tableHtml += `
              <tr>
                <td>${category.id}</td>
                <td>${category.name}</td>
                <td>${category.slug}</td>
                <td>${new Date(category.created_at).toLocaleString()}</td>
                <td class="actions">
                  <button onclick="editCategory('${category.id}', '${category.name}', '${category.slug}')">Edit</button>
                  <button onclick="deleteCategory('${category.id}')">Delete</button>
                </td>
              </tr>
            `;
            
            // Add option to select
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            editCategoryId.appendChild(option);
          });
          
          tableHtml += `
              </tbody>
            </table>
          `;
          
          categoriesList.innerHTML = tableHtml;
        } else {
          categoriesList.innerHTML = `<span class="error">Error: ${data.error}</span>`;
        }
      } catch (error) {
        categoriesList.innerHTML = `<span class="error">Error: ${error.message}</span>`;
      }
    }
    
    // Add a new category
    async function addCategory() {
      const nameInput = document.getElementById('category-name');
      const slugInput = document.getElementById('category-slug');
      const addResult = document.getElementById('add-result');
      
      const name = nameInput.value.trim();
      const slug = slugInput.value.trim();
      
      if (!name || !slug) {
        addResult.innerHTML = '<span class="error">Name and slug are required</span>';
        return;
      }
      
      addResult.innerHTML = 'Adding category...';
      
      try {
        const response = await fetch('/api/admin/categories', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ name, slug })
        });
        
        const data = await response.json();
        
        if (data.success) {
          addResult.innerHTML = `<span class="success">Category added successfully!</span>`;
          nameInput.value = '';
          slugInput.value = '';
          
          // Refresh categories list
          fetchCategories();
        } else {
          addResult.innerHTML = `<span class="error">Error: ${data.error}</span>`;
        }
      } catch (error) {
        addResult.innerHTML = `<span class="error">Error: ${error.message}</span>`;
      }
    }
    
    // Edit a category
    function editCategory(id, name, slug) {
      document.getElementById('edit-category-id').value = id;
      document.getElementById('edit-category-name').value = name;
      document.getElementById('edit-category-slug').value = slug;
    }
    
    // Update a category
    async function updateCategory() {
      const idSelect = document.getElementById('edit-category-id');
      const nameInput = document.getElementById('edit-category-name');
      const slugInput = document.getElementById('edit-category-slug');
      const editResult = document.getElementById('edit-result');
      
      const id = idSelect.value;
      const name = nameInput.value.trim();
      const slug = slugInput.value.trim();
      
      if (!id || !name || !slug) {
        editResult.innerHTML = '<span class="error">ID, name, and slug are required</span>';
        return;
      }
      
      editResult.innerHTML = 'Updating category...';
      
      try {
        const response = await fetch(`/api/admin/categories/${id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ name, slug })
        });
        
        const data = await response.json();
        
        if (data.success) {
          editResult.innerHTML = `<span class="success">Category updated successfully!</span>`;
          
          // Reset form
          idSelect.value = '';
          nameInput.value = '';
          slugInput.value = '';
          
          // Refresh categories list
          fetchCategories();
        } else {
          editResult.innerHTML = `<span class="error">Error: ${data.error}</span>`;
        }
      } catch (error) {
        editResult.innerHTML = `<span class="error">Error: ${error.message}</span>`;
      }
    }
    
    // Delete a category
    async function deleteCategory(id) {
      if (!confirm('Are you sure you want to delete this category?')) {
        return;
      }
      
      const editResult = document.getElementById('edit-result');
      
      if (!id) {
        editResult.innerHTML = '<span class="error">Category ID is required</span>';
        return;
      }
      
      editResult.innerHTML = 'Deleting category...';
      
      try {
        const response = await fetch(`/api/admin/categories/${id}`, {
          method: 'DELETE'
        });
        
        const data = await response.json();
        
        if (data.success) {
          editResult.innerHTML = `<span class="success">Category deleted successfully!</span>`;
          
          // Reset form
          document.getElementById('edit-category-id').value = '';
          document.getElementById('edit-category-name').value = '';
          document.getElementById('edit-category-slug').value = '';
          
          // Refresh categories list
          fetchCategories();
        } else {
          editResult.innerHTML = `<span class="error">Error: ${data.error}</span>`;
        }
      } catch (error) {
        editResult.innerHTML = `<span class="error">Error: ${error.message}</span>`;
      }
    }
    
    // Initialize
    document.addEventListener('DOMContentLoaded', () => {
      // Fetch categories on page load
      fetchCategories();
      
      // Add event listeners
      document.getElementById('fetch-categories-btn').addEventListener('click', fetchCategories);
      document.getElementById('add-category-btn').addEventListener('click', addCategory);
      document.getElementById('update-category-btn').addEventListener('click', updateCategory);
      document.getElementById('delete-category-btn').addEventListener('click', () => {
        const id = document.getElementById('edit-category-id').value;
        if (id) {
          deleteCategory(id);
        } else {
          document.getElementById('edit-result').innerHTML = '<span class="error">Please select a category to delete</span>';
        }
      });
      
      // Auto-generate slug from name
      document.getElementById('category-name').addEventListener('input', (e) => {
        const name = e.target.value.trim();
        const slug = name.toLowerCase()
          .replace(/[^\w\s-]/g, '') // Remove special characters
          .replace(/\s+/g, '-') // Replace spaces with hyphens
          .replace(/-+/g, '-'); // Replace multiple hyphens with a single hyphen
        
        document.getElementById('category-slug').value = slug;
      });
      
      document.getElementById('edit-category-name').addEventListener('input', (e) => {
        const name = e.target.value.trim();
        const slug = name.toLowerCase()
          .replace(/[^\w\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-');
        
        document.getElementById('edit-category-slug').value = slug;
      });
    });
  </script>
</body>
</html>
