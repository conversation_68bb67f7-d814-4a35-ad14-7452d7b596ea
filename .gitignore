# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Temporary files and scripts (created during development/troubleshooting)
test-*.js
create-admin-user.js
get-service-key.js
test-and-setup.js
test-supabase-connections.js
setup-vercel-env.md
setup-rls-policies.sql
*-FIX*.md
*-GUIDE*.md
*SUMMARY*.md
*RESTORATION*.md
DEPLOYMENT_FIX_SUMMARY.md
GITHUB_UPLOAD_GUIDE.md
MIGRATION_SUMMARY.md
TROUBLESHOOTING.md
TESTING.md
SECURITY_FIXES.md
VERCEL_*.md
CLOUDFLARE_*.md
SUPABASE_*.md
DATABASE_FIX.md
BUILD_ERRORS_*.md
CHANNEL_ERROR_*.md
R2_UPLOAD_*.md
RSS_GUIDE.md
SETUP_GUIDE.md
deploy-*.js
deploy-*.md
push-to-github.*
upload-to-github.*
*.bat
set-admin-creds.js
setup-admin.js
setup_env.js
update_admin_menu.js
add-sample-data.js
add-test-article.js
check-all-tables.js
fix-branding.js
flipnews_schema.sql
supabase-schema.sql
