/**
 * Cloudinary Service
 *
 * This service provides functions for uploading images and videos to Cloudinary.
 * It serves as an alternative to Supabase Storage.
 */

// We need to use a browser-compatible approach for Cloudinary
// For client-side uploads, we'll use the Cloudinary Upload API with signatures
// generated by our server-side API route
const cloudinary = {
  config: (config?: unknown) => {
    console.log('Cloudinary config called with:', config);
    return {};
  },
  uploader: {
    upload: async () => ({ secure_url: null }),
  },
  utils: {
    api_sign_request: (params: unknown, secret: string) => {
      // This is just a placeholder - the actual signing happens server-side
      console.log('Cloudinary signature requested for:', params);
      return 'mock-signature';
    },
  },
};

// Cloudinary configuration - hardcoded for now to ensure they work
cloudinary.config({
  cloud_name: 'dejesejon',
  api_key: '137179496379745',
  api_secret: '2iwEKWNqCHLtSWKu9KvFv06zpDw',
  secure: true,
});

console.log('Cloudinary configured with cloud_name:', 'dejesejon');

// Define folder names for different types of media
const FOLDERS = {
  NEWS_IMAGES: 'news-images',
  NEWS_VIDEOS: 'news-videos',
  USER_AVATARS: 'user-avatars',
  SITE_ASSETS: 'site-assets',
};

/**
 * Uploads a file to Cloudinary
 *
 * @param file The file to upload (as a base64 string or URL)
 * @param options Upload options
 * @returns Promise with upload result
 */
const uploadToCloudinary = async (
  file: string,
  options: {
    folder: string;
    resource_type: 'image' | 'video' | 'auto';
    public_id?: string;
    tags?: string[];
    transformation?: unknown;
  }
): Promise<any> => {
  try {
    console.log('Starting Cloudinary upload process...', {
      folder: options.folder,
      resource_type: options.resource_type,
      fileLength: file.length,
      isBase64: file.startsWith('data:')
    });

    // For server-side uploads
    if (typeof window === 'undefined') {
      console.log('Server-side upload not supported in this implementation');
      throw new Error('Server-side upload not supported');
    }

    // For client-side uploads, we need to use the upload widget or a signed upload
    console.log('Getting upload signature from API...');
    const response = await fetch('/api/cloudinary/signature', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        folder: options.folder,
        resource_type: options.resource_type,
        public_id: options.public_id,
        tags: options.tags,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Failed to get upload signature:', errorText);
      console.error('Response status:', response.status);
      console.error('Response headers:', Object.fromEntries([...response.headers.entries()]));
      throw new Error(`Failed to get upload signature: ${errorText}`);
    }

    const responseData = await response.json();
    console.log('Signature API response:', responseData);

    const { signature, timestamp, api_key, cloud_name } = responseData;

    if (!signature || !timestamp || !api_key || !cloud_name) {
      console.error('Missing required fields in signature response:', responseData);
      throw new Error('Missing required fields in signature response');
    }

    console.log('Received signature from API:', {
      signature: signature.substring(0, 10) + '...',
      timestamp,
      api_key: api_key.substring(0, 5) + '...',
      cloud_name
    });

    // Create form data for upload
    const formData = new FormData();

    // Handle base64 data URLs
    if (file.startsWith('data:')) {
      console.log('Uploading base64 data URL...');
      formData.append('file', file);
    } else {
      // Handle file objects or URLs
      console.log('Uploading file or URL...');
      formData.append('file', file);
    }

    formData.append('api_key', api_key);
    formData.append('timestamp', timestamp.toString());
    formData.append('signature', signature);
    formData.append('folder', options.folder);

    if (options.public_id) {
      formData.append('public_id', options.public_id);
    }

    if (options.tags && Array.isArray(options.tags)) {
      formData.append('tags', options.tags.join(','));
    }

    // Get cloud name from response or environment variable
    const cloudName = cloud_name || 'dejesejon';
    console.log('Using cloud name:', cloudName);

    // Upload to Cloudinary
    const uploadUrl = `https://api.cloudinary.com/v1_1/${cloudName}/${options.resource_type}/upload`;
    console.log(`Uploading to Cloudinary API: ${uploadUrl}`);

    const uploadResponse = await fetch(uploadUrl, {
      method: 'POST',
      body: formData,
    });

    if (!uploadResponse.ok) {
      const errorText = await uploadResponse.text();
      console.error('Failed to upload to Cloudinary:', errorText);
      console.error('Upload response status:', uploadResponse.status);
      console.error('Upload response headers:', Object.fromEntries([...uploadResponse.headers.entries()]));
      throw new Error(`Failed to upload to Cloudinary: ${errorText}`);
    }

    const result = await uploadResponse.json();
    console.log('Cloudinary upload successful:', {
      public_id: result.public_id,
      url: result.secure_url
    });

    return result;
  } catch (error: any) {
    console.error('Error uploading to Cloudinary:', error);
    console.error('Error message:', error.message);
    if (error.stack) {
      console.error('Error stack:', error.stack);
    }
    throw error;
  }
};

/**
 * Uploads an image to Cloudinary
 *
 * @param file The image file (as a base64 string or URL)
 * @param folder The folder to upload to (defaults to news-images)
 * @returns Promise with the upload result
 */
export const uploadImage = async (
  file: File | string,
  folder: 'news-images' | 'user-avatars' | 'site-assets' = 'news-images'
): Promise<{ url: string | null; error: string | null }> => {
  try {
    console.log('Uploading image to Cloudinary...', {
      fileType: file instanceof File ? file.type : 'string',
      fileSize: file instanceof File ? file.size : 'N/A',
      folder
    });

    // Convert File to base64 if needed
    let fileData = file;
    if (file instanceof File) {
      console.log('Converting File to base64...');
      fileData = await fileToBase64(file);
      console.log('File converted to base64, length:', (fileData as string).length);
    }

    console.log('Calling uploadToCloudinary...');
    const result = await uploadToCloudinary(fileData as string, {
      folder,
      resource_type: 'image',
      tags: folder ? ['flipnews', folder] : ['flipnews'],
    });

    if (!result) {
      console.error('No result returned from uploadToCloudinary');
      return {
        url: null,
        error: 'No result returned from Cloudinary upload',
      };
    }

    if (!result.secure_url) {
      console.error('No secure URL returned from Cloudinary, result:', result);
      return {
        url: null,
        error: 'Failed to get secure URL from Cloudinary',
      };
    }

    console.log('Image uploaded to Cloudinary successfully:', result.secure_url);
    return {
      url: result.secure_url,
      error: null,
    };
  } catch (error: any) {
    console.error('Error uploading image to Cloudinary:', error);
    console.error('Error message:', error.message);
    if (error.stack) {
      console.error('Error stack:', error.stack);
    }
    return {
      url: null,
      error: error.message || 'Failed to upload image',
    };
  }
};

/**
 * Uploads a video to Cloudinary
 *
 * @param file The video file (as a base64 string or URL)
 * @returns Promise with the upload result
 */
export const uploadVideo = async (
  file: File | string
): Promise<{ url: string | null; error: string | null }> => {
  try {
    console.log('Uploading video to Cloudinary...');

    // Convert File to base64 if needed
    let fileData = file;
    if (file instanceof File) {
      fileData = await fileToBase64(file);
    }

    const result = await uploadToCloudinary(fileData as string, {
      folder: FOLDERS.NEWS_VIDEOS,
      resource_type: 'video',
      tags: ['flipnews', 'video'],
    });

    if (!result || !result.secure_url) {
      console.error('No secure URL returned from Cloudinary');
      return {
        url: null,
        error: 'Failed to get secure URL from Cloudinary',
      };
    }

    console.log('Video uploaded to Cloudinary successfully:', result.secure_url);
    return {
      url: result.secure_url,
      error: null,
    };
  } catch (error: unknown) {
    console.error('Error uploading video to Cloudinary:', error);
    return {
      url: null,
      error: error.message || 'Failed to upload video',
    };
  }
};

/**
 * Converts a File object to a base64 string
 *
 * @param file The file to convert
 * @returns Promise with the base64 string
 */
const fileToBase64 = (file: File): Promise<string> => {
  // Check if we're in a browser environment
  if (typeof window !== 'undefined' && typeof FileReader !== 'undefined') {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });
  } else {
    // Server-side handling
    console.log('Server-side file handling is not supported in this implementation');
    return Promise.reject('FileReader is not available in this environment');
  }
};

/**
 * Checks if Cloudinary is properly configured
 *
 * @returns Boolean indicating if Cloudinary is configured
 */
export const isCloudinaryConfigured = (): boolean => {
  // Always return true since we're using hardcoded values
  return true;
};

export default {
  uploadImage,
  uploadVideo,
  isCloudinaryConfigured,
  FOLDERS,
};
