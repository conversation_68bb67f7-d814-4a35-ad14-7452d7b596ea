const { createClient } = require('@supabase/supabase-js');

// Test local env credentials
const localUrl = 'https://vqffolxybqjggpzeisrc.supabase.co';
const localKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZxZmZvbHh5YnFqZ2dwemVpc3JjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyMzQ3ODMsImV4cCI6MjA2ODgxMDc4M30.-48dSYw_sNrAK6AZC7xxaAPR_9wp3olOYjdh0aS36P8';

// Test vercel.json credentials  
const vercelUrl = 'https://wtwetyalktzkimwtiwun.supabase.co';
const vercelKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind0d2V0eWFsa3R6a2ltd3Rpd3VuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3MTgxNTksImV4cCI6MjA2NTI5NDE1OX0.44EU7VKJPO7W7Xpvf-X6zp58O0KuBYZ0seRTfLextR0';

async function testConnection(url, key, name) {
  try {
    console.log(`Testing ${name}...`);
    const supabase = createClient(url, key);
    const { data, error } = await supabase.from('news_articles').select('id').limit(1);
    
    if (error) {
      console.log(`${name}: Connection failed - ${error.message}`);
      return false;
    } else {
      console.log(`${name}: Connection successful - Found ${data ? data.length : 0} articles`);
      return true;
    }
  } catch (err) {
    console.log(`${name}: Connection error - ${err.message}`);
    return false;
  }
}

async function main() {
  console.log('Testing Supabase connections...\n');
  
  const localWorks = await testConnection(localUrl, localKey, 'Local (.env.local)');
  console.log('');
  const vercelWorks = await testConnection(vercelUrl, vercelKey, 'Vercel (vercel.json)');
  
  console.log('\n--- Results ---');
  console.log(`Local credentials work: ${localWorks}`);
  console.log(`Vercel credentials work: ${vercelWorks}`);
  
  if (localWorks && !vercelWorks) {
    console.log('\nRecommendation: Update vercel.json to use local credentials');
  } else if (!localWorks && vercelWorks) {
    console.log('\nRecommendation: Update .env.local to use vercel credentials');
  } else if (!localWorks && !vercelWorks) {
    console.log('\nProblem: Neither set of credentials work!');
  } else {
    console.log('\nBoth work - check which database has your data');
  }
}

main().catch(console.error);
