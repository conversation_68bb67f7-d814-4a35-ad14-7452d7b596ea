{"name": "jb5news", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "init-db": "node scripts/init-database.js", "setup-rss": "node scripts/setup-rss.js", "setup": "npm run init-db && npm run setup-rss && npm run init-categories && npm run init-storage", "install-deps": "node scripts/install-dependencies.js", "add-telugu-feeds": "node scripts/add-telugu-feeds.js", "fix-users": "node scripts/fix-users-table.js", "test-rss": "node scripts/test-rss-feeds.js", "init-categories": "node scripts/init-categories.js", "init-storage": "node scripts/init-storage.js", "deploy": "node deploy.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.810.0", "@supabase/supabase-js": "^2.49.4", "@supabase/ssr": "^0.6.1", "axios": "^1.9.0", "form-data": "^4.0.2", "framer-motion": "^12.9.4", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "next": "15.3.1", "node-fetch": "^2.7.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "rss-parser": "^3.13.0", "swiper": "^11.2.6", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "dotenv": "^16.5.0", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}}