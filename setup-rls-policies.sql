-- Row Level Security (RLS) Policies for Vizag News
-- Run this in your Supabase SQL Editor

-- First, enable RLS on all tables (if not already enabled)
ALTER TABLE news_articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE ads ENABLE ROW LEVEL SECURITY;
ALTER TABLE comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE site_settings ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Allow authenticated users full access" ON news_articles;
DROP POLICY IF EXISTS "Allow public read access to published articles" ON news_articles;
DROP POLICY IF EXISTS "Allow authenticated users full access" ON categories;
DROP POLICY IF EXISTS "Allow public read access to categories" ON categories;
DROP POLICY IF EXISTS "Allow authenticated users full access" ON ads;
DROP POLICY IF EXISTS "Allow public read access to ads" ON ads;
DROP POLICY IF EXISTS "Allow authenticated users full access" ON comments;
DROP POLICY IF EXISTS "Allow public read access to comments" ON comments;
DROP POLICY IF EXISTS "Allow authenticated users full access" ON site_settings;

-- NEWS ARTICLES POLICIES
-- Allow authenticated users (admins) full access to all articles
CREATE POLICY "Allow authenticated users full access" ON news_articles
  FOR ALL USING (auth.role() = 'authenticated');

-- Allow public read access to published articles only
CREATE POLICY "Allow public read access to published articles" ON news_articles
  FOR SELECT USING (published = true);

-- CATEGORIES POLICIES
-- Allow authenticated users (admins) full access to categories
CREATE POLICY "Allow authenticated users full access" ON categories
  FOR ALL USING (auth.role() = 'authenticated');

-- Allow public read access to all categories
CREATE POLICY "Allow public read access to categories" ON categories
  FOR SELECT USING (true);

-- ADS POLICIES
-- Allow authenticated users (admins) full access to ads
CREATE POLICY "Allow authenticated users full access" ON ads
  FOR ALL USING (auth.role() = 'authenticated');

-- Allow public read access to active ads only
CREATE POLICY "Allow public read access to active ads" ON ads
  FOR SELECT USING (active = true);

-- COMMENTS POLICIES
-- Allow authenticated users (admins) full access to comments
CREATE POLICY "Allow authenticated users full access" ON comments
  FOR ALL USING (auth.role() = 'authenticated');

-- Allow public read access to all comments
CREATE POLICY "Allow public read access to comments" ON comments
  FOR SELECT USING (true);

-- Allow anyone to insert comments (for public commenting)
CREATE POLICY "Allow public comment insertion" ON comments
  FOR INSERT WITH CHECK (true);

-- SITE SETTINGS POLICIES
-- Allow authenticated users (admins) full access to site settings
CREATE POLICY "Allow authenticated users full access" ON site_settings
  FOR ALL USING (auth.role() = 'authenticated');

-- Allow public read access to site settings
CREATE POLICY "Allow public read access to site settings" ON site_settings
  FOR SELECT USING (true);

-- Verify policies were created
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public' 
ORDER BY tablename, policyname;
