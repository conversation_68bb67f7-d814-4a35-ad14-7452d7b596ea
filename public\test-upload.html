<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test R2 Upload - Vizag News</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            text-align: center;
        }
        input[type="file"] {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 100%;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 4px;
            min-height: 50px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .loading {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        img {
            max-width: 100%;
            max-height: 300px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .url-link {
            word-break: break-all;
            color: #007bff;
            text-decoration: none;
        }
        .url-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Vizag News - R2 Upload Test</h1>
        
        <div class="upload-section">
            <h3>📤 Test Image Upload to Cloudflare R2</h3>
            <p>Select an image file to test the upload functionality:</p>
            
            <input type="file" id="imageFile" accept="image/*" />
            <br>
            <button onclick="uploadImage()" id="uploadBtn">Upload Image</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>
        
        <div id="result" class="result info">
            <strong>ℹ️ Ready to test upload</strong><br>
            Select an image file and click "Upload Image" to test the R2 upload functionality.
        </div>
    </div>

    <script>
        async function uploadImage() {
            const fileInput = document.getElementById('imageFile');
            const resultDiv = document.getElementById('result');
            const uploadBtn = document.getElementById('uploadBtn');
            
            // Check if file is selected
            if (!fileInput.files || fileInput.files.length === 0) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '<strong>❌ Error</strong><br>Please select an image file first.';
                return;
            }
            
            const file = fileInput.files[0];
            
            // Validate file type
            if (!file.type.startsWith('image/')) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '<strong>❌ Error</strong><br>Please select a valid image file.';
                return;
            }
            
            // Show loading state
            uploadBtn.disabled = true;
            uploadBtn.textContent = 'Uploading...';
            resultDiv.className = 'result loading';
            resultDiv.innerHTML = `<strong>⏳ Uploading...</strong><br>Uploading "${file.name}" (${Math.round(file.size/1024)}KB) to Cloudflare R2...`;
            
            try {
                // Create form data
                const formData = new FormData();
                formData.append('file', file);
                formData.append('folder', 'news-images');
                
                console.log('Uploading file:', file.name, 'Size:', file.size, 'Type:', file.type);
                
                // Make the upload request
                const response = await fetch('/api/upload/r2', {
                    method: 'POST',
                    body: formData
                });
                
                console.log('Response status:', response.status);
                
                const data = await response.json();
                console.log('Response data:', data);
                
                if (data.success && data.url) {
                    // Success
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>✅ Upload Successful!</strong><br>
                        <strong>File:</strong> ${file.name}<br>
                        <strong>Size:</strong> ${Math.round(file.size/1024)}KB<br>
                        <strong>Provider:</strong> ${data.provider || 'R2'}<br>
                        <strong>URL:</strong> <a href="${data.url}" target="_blank" class="url-link">${data.url}</a><br>
                        <br>
                        <img src="${data.url}" alt="Uploaded image" />
                    `;
                } else {
                    // Error from API
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <strong>❌ Upload Failed</strong><br>
                        <strong>Error:</strong> ${data.error || 'Unknown error'}<br>
                        ${data.details ? `<strong>Details:</strong> ${data.details}<br>` : ''}
                        <strong>Response Status:</strong> ${response.status}
                    `;
                }
                
            } catch (error) {
                // Network or other error
                console.error('Upload error:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <strong>❌ Upload Failed</strong><br>
                    <strong>Error:</strong> ${error.message}<br>
                    <strong>Type:</strong> Network/Connection Error
                `;
            } finally {
                // Reset button
                uploadBtn.disabled = false;
                uploadBtn.textContent = 'Upload Image';
            }
        }
        
        function clearResults() {
            const resultDiv = document.getElementById('result');
            const fileInput = document.getElementById('imageFile');
            
            resultDiv.className = 'result info';
            resultDiv.innerHTML = `
                <strong>ℹ️ Ready to test upload</strong><br>
                Select an image file and click "Upload Image" to test the R2 upload functionality.
            `;
            fileInput.value = '';
        }
        
        // Add drag and drop functionality
        const uploadSection = document.querySelector('.upload-section');
        
        uploadSection.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadSection.style.borderColor = '#007bff';
            uploadSection.style.backgroundColor = '#f8f9fa';
        });
        
        uploadSection.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadSection.style.borderColor = '#ddd';
            uploadSection.style.backgroundColor = 'transparent';
        });
        
        uploadSection.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadSection.style.borderColor = '#ddd';
            uploadSection.style.backgroundColor = 'transparent';
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                document.getElementById('imageFile').files = files;
            }
        });
    </script>
</body>
</html>
