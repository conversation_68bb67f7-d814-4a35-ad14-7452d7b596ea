'use client';

import Link from 'next/link';
import { FiMail, FiPhone, FiMapPin, FiFacebook, FiTwitter, FiInstagram } from 'react-icons/fi';
import { useSettings } from '../contexts/SettingsContext';

export default function Footer() {
  const currentYear = new Date().getFullYear();
  const { settings } = useSettings();

  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-10">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <h3 className="text-xl font-bold mb-4">{settings.site_name || 'FlipNews'}</h3>
            <p className="text-gray-400 mb-4">
              Your source for the latest news and updates from around the world.
              Flip through stories that matter to you.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <FiFacebook size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <FiTwitter size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <FiInstagram size={20} />
              </a>
            </div>
          </div>

          <div>
            <h3 className="text-xl font-bold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-gray-400 hover:text-white transition-colors">
                  Home
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-gray-400 hover:text-white transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-400 hover:text-white transition-colors">
                  Contact
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="text-gray-400 hover:text-white transition-colors">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="/terms" className="text-gray-400 hover:text-white transition-colors">
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link href="/debug" className="text-gray-400 hover:text-white transition-colors">
                  Debug Tools
                </Link>
              </li>
              <li>
                <Link href="/setup" className="text-gray-400 hover:text-white transition-colors">
                  Supabase Setup
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-xl font-bold mb-4">Contact Us</h3>
            <ul className="space-y-3">
              <li className="flex items-start">
                <FiMapPin className="mt-1 mr-3 text-gray-400" />
                <span className="text-gray-400">
                  123 News Street, Media City, NY 10001, USA
                </span>
              </li>
              <li className="flex items-center">
                <FiPhone className="mr-3 text-gray-400" />
                <span className="text-gray-400">+****************</span>
              </li>
              <li className="flex items-center">
                <FiMail className="mr-3 text-gray-400" />
                <span className="text-gray-400">contact@{settings.share_link ? new URL(settings.share_link).hostname : 'flipnews.com'}</span>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-10 pt-6 text-center text-gray-400">
          <p>&copy; {currentYear} {settings.site_name || 'FlipNews'}. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}
