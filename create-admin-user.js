const { createClient } = require('@supabase/supabase-js');
const readline = require('readline');

// Use the correct Supabase credentials
const supabaseUrl = 'https://wtwetyalktzkimwtiwun.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind0d2V0eWFsa3R6a2ltd3Rpd3VuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3MTgxNTksImV4cCI6MjA2NTI5NDE1OX0.44EU7VKJPO7W7Xpvf-X6zp58O0KuBYZ0seRTfLextR0';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function createAdminUser() {
  console.log('🔐 Creating Admin User for Vizag News');
  console.log('=====================================\n');

  try {
    // Test connection first
    console.log('Testing Supabase connection...');
    const { data: testData, error: testError } = await supabase
      .from('news_articles')
      .select('id')
      .limit(1);

    if (testError) {
      console.error('❌ Connection failed:', testError.message);
      process.exit(1);
    }

    console.log('✅ Connected to Supabase successfully!\n');

    // Get admin details
    const email = await askQuestion('Enter admin email (e.g., <EMAIL>): ');
    const password = await askQuestion('Enter admin password (min 6 characters): ');

    if (!email || !password) {
      console.log('❌ Email and password are required');
      process.exit(1);
    }

    if (password.length < 6) {
      console.log('❌ Password must be at least 6 characters');
      process.exit(1);
    }

    console.log('\n🔄 Creating admin user...');

    // Create user with Supabase Auth
    const { data, error } = await supabase.auth.signUp({
      email: email,
      password: password,
      options: {
        emailRedirectTo: undefined // Skip email confirmation for admin
      }
    });

    if (error) {
      console.error('❌ Failed to create user:', error.message);
      
      if (error.message.includes('already registered')) {
        console.log('\n💡 User already exists. You can use these credentials to login.');
      }
    } else if (data.user) {
      console.log('✅ Admin user created successfully!');
      console.log(`📧 Email: ${email}`);
      console.log(`🆔 User ID: ${data.user.id}`);
      
      if (data.user.email_confirmed_at) {
        console.log('✅ Email confirmed');
      } else {
        console.log('⚠️  Email confirmation may be required');
        console.log('   Check your Supabase Auth settings to disable email confirmation for easier admin access');
      }
    }

    console.log('\n📋 Next Steps:');
    console.log('1. Update your Vercel environment variables');
    console.log('2. Set up RLS policies in Supabase (see setup-vercel-env.md)');
    console.log('3. Redeploy your Vercel application');
    console.log('4. Test login at your-site.vercel.app/admin/login');

  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
  } finally {
    rl.close();
  }
}

// Run the script
createAdminUser();
