import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import * as mediaService from '../../lib/mediaService.fixed';

// Define allowed file types - PNG prioritized for background images
const ALLOWED_IMAGE_TYPES = [
  'image/png',    // Prioritized for background images
  'image/jpeg',
  'image/jpg',
  'image/gif',
  'image/webp',
  'image/svg+xml'
];

const ALLOWED_VIDEO_TYPES = [
  'video/mp4',
  'video/webm',
  'video/ogg'
];

// Define max file sizes
const MAX_IMAGE_SIZE = 5 * 1024 * 1024; // 5MB
const MAX_VIDEO_SIZE = 50 * 1024 * 1024; // 50MB

// POST /api/upload - Upload a file
export async function POST(request: NextRequest) {
  try {
    console.log('Upload API called');

    // Get form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const type = formData.get('type') as string || 'image';
    const bucket = formData.get('bucket') as string || 'news-images';
    const preferredProvider = formData.get('provider') as string || undefined;

    console.log('Upload request details:', {
      fileType: file?.type,
      fileSize: file?.size,
      type,
      bucket,
      preferredProvider
    });

    // Validate file
    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    if (type === 'image' && !ALLOWED_IMAGE_TYPES.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid image type. Allowed types: ' + ALLOWED_IMAGE_TYPES.join(', ') },
        { status: 400 }
      );
    }

    if (type === 'video' && !ALLOWED_VIDEO_TYPES.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid video type. Allowed types: ' + ALLOWED_VIDEO_TYPES.join(', ') },
        { status: 400 }
      );
    }

    // Validate file size
    if (type === 'image' && file.size > MAX_IMAGE_SIZE) {
      return NextResponse.json(
        { error: `Image size exceeds maximum allowed size (${MAX_IMAGE_SIZE / 1024 / 1024}MB)` },
        { status: 400 }
      );
    }

    if (type === 'video' && file.size > MAX_VIDEO_SIZE) {
      return NextResponse.json(
        { error: `Video size exceeds maximum allowed size (${MAX_VIDEO_SIZE / 1024 / 1024}MB)` },
        { status: 400 }
      );
    }

    // Generate a unique file name
    let fileExt = file.name.split('.').pop();

    // For site-assets folder (background images), prefer PNG extension
    if (bucket === 'site-assets' && file.type === 'image/png') {
      fileExt = 'png';
    }

    const fileName = `${uuidv4()}.${fileExt}`;

    // Get configured providers
    const configuredProviders = await mediaService.getConfiguredProviders();
    console.log('Configured storage providers:', configuredProviders);

    // Upload the file using the media service
    let result;
    if (type === 'image') {
      result = await mediaService.uploadImage(file, {
        folder: bucket as any,
        preferredProvider: preferredProvider as any,
        fileName
      });
    } else {
      result = await mediaService.uploadVideo(file, {
        preferredProvider: preferredProvider as any,
        fileName
      });
    }

    if (!result.url) {
      return NextResponse.json(
        {
          error: 'Failed to upload file',
          details: result.error || 'No storage provider available',
          provider: result.provider
        },
        { status: 500 }
      );
    }

    // Make sure the URL is properly formatted
    let publicUrl = result.url;

    // Log the URL for debugging
    console.log(`Generated public URL (via ${result.provider}):`, publicUrl);

    // Ensure the URL has the correct protocol
    if (!publicUrl.startsWith('http')) {
      publicUrl = `https://${publicUrl.replace(/^\/\//, '')}`;
    }

    // Add cache-busting parameter to prevent caching issues
    publicUrl = `${publicUrl}?t=${Date.now()}`;

    return NextResponse.json({
      success: true,
      url: publicUrl,
      provider: result.provider
    });
  } catch (error: unknown) {
    console.error('Error in upload API:', error);
    return NextResponse.json(
      { error: 'An error occurred', details: error.message },
      { status: 500 }
    );
  }
}
