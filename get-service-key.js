// This script helps identify the correct service role key
// The service role key should have 'service_role' in the payload, not 'anon'

const jwt = require('jsonwebtoken');

const anonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind0d2V0eWFsa3R6a2ltd3Rpd3VuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3MTgxNTksImV4cCI6MjA2NTI5NDE1OX0.44EU7VKJPO7W7Xpvf-X6zp58O0KuBYZ0seRTfLextR0';

console.log('Analyzing JWT tokens...\n');

try {
  const decoded = jwt.decode(anonKey);
  console.log('Anon Key payload:', JSON.stringify(decoded, null, 2));
  
  if (decoded.role === 'anon') {
    console.log('\n⚠️  The SUPABASE_SERVICE_ROLE_KEY in vercel.json is actually an anon key!');
    console.log('You need to get the actual service_role key from your Supabase dashboard.');
    console.log('\nTo get the service role key:');
    console.log('1. Go to https://supabase.com/dashboard');
    console.log('2. Select your project: wtwetyalktzkimwtiwun');
    console.log('3. Go to Settings > API');
    console.log('4. Copy the "service_role" key (not the "anon public" key)');
  }
} catch (error) {
  console.error('Error decoding JWT:', error.message);
}
