@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary-color: #FACC15;
  --primary-rgb: 250, 204, 21;
  --secondary-color: #000000;
  --secondary-rgb: 0, 0, 0;
  --success-color: #10B981;
  --success-rgb: 16, 185, 129;
  --error-color: #EF4444;
  --error-rgb: 239, 68, 68;
  --warning-color: #F59E0B;
  --warning-rgb: 245, 158, 11;
  --info-color: #3B82F6;
  --info-rgb: 59, 130, 246;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary-color);
  --color-secondary: var(--secondary-color);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html, body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* For full-screen cards */
#__next, main {
  height: 100%;
  width: 100%;
}

/* Custom styles for flip cards */
.perspective-1000 {
  perspective: 1000px;
}

.preserve-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Vertical swipe styles */
.swipe-container {
  height: 100vh;
  width: 100%;
  overflow: hidden;
  position: relative;
}

.swipe-card {
  height: 100%;
  width: 100%;
  position: absolute;
  transition: transform 0.5s ease;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-7 {
  display: -webkit-box;
  -webkit-line-clamp: 7;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-8 {
  display: -webkit-box;
  -webkit-line-clamp: 8;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Admin panel styles */
.admin-panel input,
.admin-panel textarea,
.admin-panel select {
  color: #000000 !important;
}

/* WhatsApp share button animations */
@keyframes pulse-whatsapp {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
  }

  70% {
    transform: scale(1.1);
    box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
  }

  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
  }
}

.whatsapp-pulse {
  animation: pulse-whatsapp 2s infinite;
}

/* WhatsApp button styles */
.whatsapp-share-container {
  /* Make sure it's always visible */
  opacity: 1 !important;
  visibility: visible !important;
}

/* Fix admin panel scrolling */
.admin-content {
  max-height: calc(100vh - 80px);
  overflow-y: auto;
  padding-bottom: 100px;
}

/* Improved popup scrolling */
.overflow-y-auto {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Allow content to be scrollable in modals */
.modal-content {
  overscroll-behavior: contain;
  -webkit-overflow-scrolling: touch;
}

/* Dynamic color utility classes */
.bg-primary {
  background-color: var(--primary-color) !important;
}

.bg-primary-10 {
  background-color: rgba(var(--primary-rgb), 0.1) !important;
}

.bg-primary-20 {
  background-color: rgba(var(--primary-rgb), 0.2) !important;
}

.bg-primary-50 {
  background-color: rgba(var(--primary-rgb), 0.5) !important;
}

.bg-primary-70 {
  background-color: rgba(var(--primary-rgb), 0.7) !important;
}

.bg-primary-80 {
  background-color: rgba(var(--primary-rgb), 0.8) !important;
}

.bg-primary-90 {
  background-color: rgba(var(--primary-rgb), 0.9) !important;
}

.text-primary {
  color: var(--primary-color) !important;
}

.border-primary {
  border-color: var(--primary-color) !important;
}

.bg-secondary {
  background-color: var(--secondary-color) !important;
}

.bg-secondary-10 {
  background-color: rgba(var(--secondary-rgb), 0.1) !important;
}

.bg-secondary-20 {
  background-color: rgba(var(--secondary-rgb), 0.2) !important;
}

.bg-secondary-50 {
  background-color: rgba(var(--secondary-rgb), 0.5) !important;
}

.text-secondary {
  color: var(--secondary-color) !important;
}

.border-secondary {
  border-color: var(--secondary-color) !important;
}

/* Connection status indicator */
.connection-indicator {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 50;
}

/* Background logo styles - will be dynamically updated */
.content-with-logo-bg {
  position: relative;
}

.content-with-logo-bg::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 70%;
  height: 70%;
  background-image: var(--background-logo-url, url('/logo-background.svg'));
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;
  opacity: var(--background-logo-opacity, 0.1);
  pointer-events: none;
  z-index: 1;
}

.content-with-logo-bg > * {
  position: relative;
  z-index: 2;
}
