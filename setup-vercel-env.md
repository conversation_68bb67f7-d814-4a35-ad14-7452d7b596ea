# Vercel Environment Variables Setup

## Issues Found:
1. Your local `.env.local` and `vercel.json` have different Supabase credentials
2. The `SUPABASE_SERVICE_ROLE_KEY` in `vercel.json` is actually an anon key (not a service role key)
3. Authentication is not working because of incorrect environment variables

## Steps to Fix:

### 1. Get the Correct Service Role Key
1. Go to https://supabase.com/dashboard
2. Select your project: `wtwetyalktzkimwtiwun`
3. Go to **Settings** > **API**
4. Copy the **service_role** key (NOT the anon public key)

### 2. Update Vercel Environment Variables
Since you already have the project deployed on Vercel, you need to:

1. Go to your Vercel dashboard: https://vercel.com/dashboard
2. Find your project (likely named "vizag-news" or similar)
3. Go to **Settings** > **Environment Variables**
4. Add/Update these variables:

```
NEXT_PUBLIC_SUPABASE_URL=https://wtwetyalktzkimwtiwun.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind0d2V0eWFsa3R6a2ltd3Rpd3VuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3MTgxNTksImV4cCI6MjA2NTI5NDE1OX0.44EU7VKJPO7W7Xpvf-X6zp58O0KuBYZ0seRTfLextR0
SUPABASE_SERVICE_ROLE_KEY=[YOUR_ACTUAL_SERVICE_ROLE_KEY_HERE]
```

### 3. Create Admin User in Supabase
Since we've implemented proper Supabase Auth, you need to create an admin user:

1. Go to your Supabase dashboard
2. Go to **Authentication** > **Users**
3. Click **Add User**
4. Create a user with:
   - Email: `<EMAIL>` (or your preferred admin email)
   - Password: Choose a secure password
   - Email Confirm: Yes

### 4. Set Up Row Level Security (RLS) Policies
Your Supabase tables need proper RLS policies for admin access:

1. Go to **SQL Editor** in Supabase
2. Run this SQL to allow authenticated users to manage data:

```sql
-- Allow authenticated users (admins) to manage all data
CREATE POLICY "Allow authenticated users full access" ON news_articles
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users full access" ON categories
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users full access" ON ads
  FOR ALL USING (auth.role() = 'authenticated');

-- Allow public read access for published articles
CREATE POLICY "Allow public read access to published articles" ON news_articles
  FOR SELECT USING (published = true);

CREATE POLICY "Allow public read access to categories" ON categories
  FOR SELECT USING (true);
```

### 5. Redeploy Your Application
After updating the environment variables in Vercel:
1. Go to your project's **Deployments** tab
2. Click **Redeploy** on the latest deployment
3. Or push a new commit to trigger a deployment

### 6. Test the Setup
1. Visit your deployed site
2. Go to `/admin/login`
3. Use the admin credentials you created in Supabase
4. Try creating a news article to test data storage

## Current Status:
- ✅ Environment variables updated locally
- ✅ Authentication system implemented with Supabase Auth
- ✅ API routes fixed to use Supabase instead of mock data
- ⏳ Need to update Vercel environment variables
- ⏳ Need to create admin user in Supabase
- ⏳ Need to set up RLS policies
