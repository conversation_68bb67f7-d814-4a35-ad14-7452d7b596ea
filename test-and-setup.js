const { createClient } = require('@supabase/supabase-js');

// Use the correct Supabase credentials
const supabaseUrl = 'https://wtwetyalktzkimwtiwun.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind0d2V0eWFsa3R6a2ltd3Rpd3VuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3MTgxNTksImV4cCI6MjA2NTI5NDE1OX0.44EU7VKJPO7W7Xpvf-X6zp58O0KuBYZ0seRTfLextR0';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testAndSetup() {
  console.log('🔍 Testing Supabase Connection and Setup');
  console.log('=========================================\n');

  try {
    // Test basic connection
    console.log('1. Testing connection...');
    const { data: articles, error: articlesError } = await supabase
      .from('news_articles')
      .select('id, title')
      .limit(5);

    if (articlesError) {
      console.error('❌ Connection failed:', articlesError.message);
      return;
    }

    console.log(`✅ Connected! Found ${articles.length} articles`);
    if (articles.length > 0) {
      console.log('   Sample articles:');
      articles.forEach(article => {
        console.log(`   - ${article.title} (ID: ${article.id})`);
      });
    }

    // Test categories
    console.log('\n2. Testing categories...');
    const { data: categories, error: categoriesError } = await supabase
      .from('categories')
      .select('*')
      .limit(5);

    if (categoriesError) {
      console.error('❌ Categories error:', categoriesError.message);
    } else {
      console.log(`✅ Found ${categories.length} categories`);
      categories.forEach(cat => {
        console.log(`   - ${cat.name} (${cat.slug})`);
      });
    }

    // Create admin user
    console.log('\n3. Creating admin user...');
    const adminEmail = '<EMAIL>';
    const adminPassword = 'admin123456'; // Strong password

    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: adminEmail,
      password: adminPassword
    });

    if (authError) {
      if (authError.message.includes('already registered')) {
        console.log('✅ Admin user already exists');
        console.log(`📧 Email: ${adminEmail}`);
        console.log(`🔑 Password: ${adminPassword}`);
      } else {
        console.error('❌ Auth error:', authError.message);
      }
    } else if (authData.user) {
      console.log('✅ Admin user created successfully!');
      console.log(`📧 Email: ${adminEmail}`);
      console.log(`🔑 Password: ${adminPassword}`);
      console.log(`🆔 User ID: ${authData.user.id}`);
    }

    // Test article creation
    console.log('\n4. Testing article creation...');
    
    // First, let's try to sign in to get proper auth
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: adminEmail,
      password: adminPassword
    });

    if (signInError) {
      console.log('⚠️  Could not sign in for article test:', signInError.message);
    } else {
      console.log('✅ Signed in successfully');
      
      // Try to create a test article
      const testArticle = {
        title: 'Test Article - ' + new Date().toISOString(),
        content: 'This is a test article to verify data storage is working.',
        summary: 'Test article summary',
        category_id: 'general',
        author: 'Admin',
        published: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data: newArticle, error: createError } = await supabase
        .from('news_articles')
        .insert([testArticle])
        .select()
        .single();

      if (createError) {
        console.error('❌ Article creation failed:', createError.message);
        console.log('   This might be due to RLS policies. Check setup-rls-policies.sql');
      } else {
        console.log('✅ Test article created successfully!');
        console.log(`   Article ID: ${newArticle.id}`);
        console.log(`   Title: ${newArticle.title}`);
      }
    }

    console.log('\n📋 Summary:');
    console.log('✅ Supabase connection working');
    console.log('✅ Admin user ready');
    console.log('✅ Database tables accessible');
    console.log('\n🚀 Next Steps:');
    console.log('1. Run the RLS policies: setup-rls-policies.sql in Supabase SQL Editor');
    console.log('2. Update Vercel environment variables (see setup-vercel-env.md)');
    console.log('3. Redeploy your Vercel application');
    console.log('4. Test login at your deployed site');

  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
  }
}

// Run the test
testAndSetup();
